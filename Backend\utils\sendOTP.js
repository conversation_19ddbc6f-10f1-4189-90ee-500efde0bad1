const twilio = require('twilio');
const sendEmail = require('./sendEmail');

/**
 * Send OTP via SMS using Twilio
 * @param {string} mobile - Mobile number to send OTP to
 * @param {string} otp - OTP code to send
 * @returns {Promise} Promise with Twilio message response
 */
const sendSmsOTP = async (mobile, otp) => {
  try {
    const client = twilio(
      process.env.TWILIO_ACCOUNT_SID,
      process.env.TWILIO_AUTH_TOKEN
    );

    const message = await client.messages.create({
      body: `Your XO Sports Hub verification code is: ${otp}. Valid for 10 minutes.`,
      from: process.env.TWILIO_PHONE_NUMBER,
      to: mobile
    });

    return message;
  } catch (error) {
    console.error('Error sending SMS OTP:', error);
    throw new Error('Failed to send SMS OTP');
  }
};

/**
 * Send OTP via Email using Nodemailer
 * @param {string} email - Email address to send OTP to
 * @param {string} otp - OTP code to send
 * @returns {Promise} Promise with email send response
 */
const sendEmailOTP = async (email, otp) => {
  try {
    await sendEmail({
      email,
      subject: 'Your XO Sports Hub Verification Code',
      message: `Your verification code is: ${otp}. This code is valid for 10 minutes.`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #333;">XO Sports Hub Verification</h2>
          <p>Your verification code is:</p>
          <div style="background-color: #f4f4f4; padding: 15px; text-align: center; font-size: 24px; letter-spacing: 5px; font-weight: bold;">
            ${otp}
          </div>
          <p>This code is valid for 10 minutes.</p>
          <p>If you didn't request this code, please ignore this email.</p>
        </div>
      `
    });

    return true;
  } catch (error) {
    console.error('Error sending Email OTP:', error);
    throw new Error('Failed to send Email OTP');
  }
};

/**
 * Send OTP via both SMS and Email
 * @param {Object} user - User object with mobile and email
 * @param {string} otp - OTP code to send
 * @returns {Promise} Promise with combined results
 */
const sendOTP = async (user, otp) => {
  const results = {
    sms: null,
    email: null,
    success: false,
    smsError: null,
    emailError: null
  };

  let smsSuccess = false;
  let emailSuccess = false;

  // Try to send SMS OTP
  if (user.mobile) {
    try {
      results.sms = await sendSmsOTP(user.mobile, otp);
      smsSuccess = true;
      console.log('✅ SMS OTP sent successfully');
    } catch (error) {
      console.error('❌ SMS OTP failed:', error.message);
      results.smsError = error.message;

      // Check if it's a Twilio geographic permission error
      if (error.message.includes('Permission to send an SMS has not been enabled for the region')) {
        console.log('🌍 Geographic permission issue detected. Continuing with email-only verification.');
      }
    }
  }

  // Try to send Email OTP
  if (user.email) {
    try {
      results.email = await sendEmailOTP(user.email, otp);
      emailSuccess = true;
      console.log('✅ Email OTP sent successfully');
    } catch (error) {
      console.error('❌ Email OTP failed:', error.message);
      results.emailError = error.message;
    }
  }

  // Success if at least one method worked
  results.success = smsSuccess || emailSuccess;

  if (!results.success) {
    console.error('❌ Both SMS and Email OTP failed');
    throw new Error('Failed to send OTP via both SMS and Email');
  }

  // Log which methods succeeded
  if (smsSuccess && emailSuccess) {
    console.log('✅ OTP sent via both SMS and Email');
  } else if (smsSuccess) {
    console.log('✅ OTP sent via SMS only (Email failed)');
  } else if (emailSuccess) {
    console.log('✅ OTP sent via Email only (SMS failed)');
  }

  return results;
};

module.exports = {
  sendOTP,
  sendSmsOTP,
  sendEmailOTP
};
